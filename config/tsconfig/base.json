{"$schema": "https://json.schemastore.org/tsconfig", "display": "Base", "compilerOptions": {"composite": false, "target": "ESNext", "moduleDetection": "force", "experimentalDecorators": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "strict": true, "strictNullChecks": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitThis": true, "noUncheckedIndexedAccess": false, "noUnusedLocals": true, "noUnusedParameters": true, "inlineSources": false, "removeComments": true, "sourceMap": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true, "preserveWatchOutput": true, "noErrorTruncation": false}, "exclude": ["node_modules", "dist"]}